
<Window x:Class="ChemLabelerPrinterClient.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:ChemLabelerPrinterClient"
        xmlns:viewModels="clr-namespace:ChemLabelerPrinterClient.ViewModels"
        mc:Ignorable="d"
        Title="ChemLabeler Printer"
        Height="450" Width="800">
    <Window.DataContext>
        <viewModels:MainViewModel />
    </Window.DataContext>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <ScrollViewer VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <ItemsControl ItemsSource="{Binding Variables}">
                    <ItemsControl.ItemTemplate>
                        <DataTemplate>
                            <Grid Margin="5">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto" />
                                    <ColumnDefinition Width="*" />
                                </Grid.ColumnDefinitions>
                                <TextBlock Text="{Binding Name}" Margin="0,0,10,0" />
                                <TextBox Grid.Column="1" Text="{Binding Value, UpdateSourceTrigger=PropertyChanged}" />
                            </Grid>
                        </DataTemplate>
                    </ItemsControl.ItemTemplate>
                </ItemsControl>
            </StackPanel>
        </ScrollViewer>

        <StackPanel Grid.Row="1" Orientation="Horizontal" HorizontalAlignment="Right" Margin="10">
            <TextBlock Text="Print Mode:" VerticalAlignment="Center" Margin="0,0,10,0" />
            <ComboBox ItemsSource="{Binding PrintServices}" SelectedItem="{Binding SelectedPrintService}" VerticalAlignment="Center" />
            <TextBlock Text="Printer:" VerticalAlignment="Center" Margin="20,0,10,0" />
            <ComboBox ItemsSource="{Binding AvailablePrinters}" SelectedItem="{Binding SelectedPrinter}" VerticalAlignment="Center" Width="150" />
            <TextBlock Text="Quantity:" VerticalAlignment="Center" Margin="20,0,10,0" />
            <TextBox Text="{Binding PrintQuantity, UpdateSourceTrigger=PropertyChanged}" Width="50" VerticalAlignment="Center" />
            <Button Content="Print" Command="{Binding PrintCommand}" Margin="20,0,0,0" Padding="10,5" />
        </StackPanel>
    </Grid>
</Window>

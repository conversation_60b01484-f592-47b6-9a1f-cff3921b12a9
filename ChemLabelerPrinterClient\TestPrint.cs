using ChemLabelerPrinterClient.Models;
using ChemLabelerPrinterClient.Services;
using System;
using System.Collections.Generic;
using System.Windows;

namespace ChemLabelerPrinterClient
{
    public class TestPrint
    {
        public static void TestPrintingLogic()
        {
            try
            {
                Console.WriteLine("=== 测试打印逻辑 ===");

                // 创建测试标签规格
                var labelSpec = new LabelSpecification
                {
                    Attributes = new LabelAttributes
                    {
                        LabelWidth = 100,  // 100mm
                        LabelLength = 30   // 30mm
                    }
                };

                // 创建测试画布内容 - 使用更大更明显的元素
                var testCanvasContent = @"[
                    {
                        ""type"": ""text"",
                        ""x"": 10,
                        ""y"": 10,
                        ""content"": ""大号测试文本 TEST"",
                        ""fontSize"": 24,
                        ""fontFamily"": ""Arial"",
                        ""fontWeight"": ""Bold"",
                        ""fontStyle"": ""Normal"",
                        ""fillColor"": ""#FF0000""
                    },
                    {
                        ""type"": ""text"",
                        ""x"": 10,
                        ""y"": 40,
                        ""content"": ""第二行文本"",
                        ""fontSize"": 18,
                        ""fontFamily"": ""Arial"",
                        ""fontWeight"": ""Normal"",
                        ""fontStyle"": ""Normal"",
                        ""fillColor"": ""#0000FF""
                    }
                ]";

                Console.WriteLine($"标签尺寸: {labelSpec.Attributes.LabelWidth}mm x {labelSpec.Attributes.LabelLength}mm");

                // 测试解析服务
                var parserService = new LabelParserService();
                var elements = parserService.ParseCanvasContent(testCanvasContent, new Dictionary<string, string>());

                Console.WriteLine($"解析到 {elements.Count} 个元素:");
                for (int i = 0; i < elements.Count; i++)
                {
                    var element = elements[i];
                    Console.WriteLine($"  元素 {i + 1}: 类型={element.Type}, 位置=({element.X}, {element.Y}), 内容={element.Content}");
                }

                // 只测试 WPF 可视化打印服务
                Console.WriteLine("\n=== 测试 WPF 可视化打印服务 ===");
                var wpfPrintService = new WpfVisualPrintingService();

                // 这会显示调试信息对话框，并保存预览图片
                // 注意：传入null作为打印机名称，这样会显示打印对话框
                wpfPrintService.Print(labelSpec, elements, 1, null);

                MessageBox.Show("WPF 打印服务测试完成！\n\n请检查：\n1. 调试信息对话框\n2. 临时文件夹中的 label_preview.png\n3. 打印对话框（如果出现）",
                    "测试完成", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                var errorMessage = $"测试过程中出现错误:\n\n错误信息: {ex.Message}\n\n详细信息: {ex.StackTrace}";
                MessageBox.Show(errorMessage, "测试错误", MessageBoxButton.OK, MessageBoxImage.Error);
                Console.WriteLine($"测试错误: {ex.Message}");
            }
        }
    }
}

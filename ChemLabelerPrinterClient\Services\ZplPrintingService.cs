
using ChemLabelerPrinterClient.Models;
using System.Collections.Generic;
using System.Text;
using System.Windows.Controls;
using System.Drawing; // Added for Bitmap
using System.Drawing.Imaging; // Added for ImageFormat
using System.IO; // Added for MemoryStream
using System;
using System.Runtime.InteropServices;
using System.Windows;

namespace ChemLabelerPrinterClient.Services
{
    public class ZplPrintingService : IPrintingService
    {
        private const double DPI = 203; // 8 dots/mm
        private const double MM_PER_INCH = 25.4;

        public void Print(LabelSpecification? labelSpec, List<CanvasElement>? elements, int quantity, string? printerName)
        {
            if (labelSpec?.Attributes == null || elements == null)
            {
                MessageBox.Show("Label specification or elements are not valid.", "Printing Error", MessageBoxButton.OK, MessageBoxImage.Error);
                return;
            }
            if (labelSpec?.Attributes == null || elements == null)
            {
                MessageBox.Show("Label specification or elements are not valid.", "Printing Error", MessageBoxButton.OK, MessageBoxImage.Error);
                return;
            }

            var zplBuilder = new StringBuilder();
            zplBuilder.AppendLine("^XA"); // Start of label

            // Set label length
            int labelLengthDots = (int)(labelSpec.Attributes.LabelLength / MM_PER_INCH * DPI);
            zplBuilder.AppendLine($"^LL{labelLengthDots}");

            // Set label width
            int labelWidthDots = (int)(labelSpec.Attributes.LabelWidth / MM_PER_INCH * DPI);
            zplBuilder.AppendLine($"^PW{labelWidthDots}");

            foreach (var element in elements)
            {
                int xPos = (int)(element.X / MM_PER_INCH * DPI);
                int yPos = (int)(element.Y / MM_PER_INCH * DPI);

                switch (element.Type)
                {
                    case "text":
                        if (element is TextElement textElement && !string.IsNullOrEmpty(textElement.Content))
                        {
                            // Note: ZPL font handling is complex. This is a simplified example.
                            // A0N = Normal orientation, 20,20 = height, width in dots
                            zplBuilder.AppendLine($"^FO{xPos},{yPos}^A0N,20,20^FD{textElement.Content}^FS");
                        }
                        break;
                    case "qrcode":
                        if (element is QrCodeElement qrCodeElement && !string.IsNullOrEmpty(qrCodeElement.Content))
                        {
                            // BQN = QR Code, 2 = version, 5 = magnification
                            zplBuilder.AppendLine($"^FO{xPos},{yPos}^BQN,2,5^FDQA,{qrCodeElement.Content}^FS");
                        }
                        break;
                    case "image":
                        if (element is ImageElement imageElement && !string.IsNullOrEmpty(imageElement.Content))
                        {
                            string zplGraphicField = ConvertImageToZplGraphicField(imageElement.Content);
                            zplBuilder.AppendLine($"^FO{xPos},{yPos}^GF{zplGraphicField}^FS");
                        }
                        break;
                }
            }

            zplBuilder.AppendLine($"^PQ{quantity}"); // Set print quantity
            zplBuilder.AppendLine("^XZ"); // End of label

            if (!string.IsNullOrEmpty(printerName))
            {
                try
                {
                    RawPrinterHelper.SendStringToPrinter(printerName, zplBuilder.ToString());
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error printing to {printerName}: {ex.Message}", "Printing Error", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            else
            {
                MessageBox.Show("No printer selected for ZPL printing.", "Printing Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private string ConvertImageToZplGraphicField(string base64Image)
        {
            // Remove data URI prefix if present
            if (base64Image.Contains(","))
            {
                base64Image = base64Image.Split(',')[1];
            }

            byte[] imageBytes = Convert.FromBase64String(base64Image);

            using (MemoryStream ms = new MemoryStream(imageBytes))
            {
                using (Bitmap originalBitmap = new Bitmap(ms))
                {
                    // Convert to 1-bit monochrome bitmap
                    Bitmap monochromeBitmap = originalBitmap.Clone(new Rectangle(0, 0, originalBitmap.Width, originalBitmap.Height), PixelFormat.Format1bppIndexed);

                    int widthBytes = (monochromeBitmap.Width + 7) / 8; // Bytes per row
                    int totalBytes = widthBytes * monochromeBitmap.Height;

                    StringBuilder hexData = new StringBuilder();

                    BitmapData bmpData = monochromeBitmap.LockBits(new Rectangle(0, 0, monochromeBitmap.Width, monochromeBitmap.Height), ImageLockMode.ReadOnly, monochromeBitmap.PixelFormat);

                    IntPtr ptr = bmpData.Scan0;

                    byte[] pixels = new byte[totalBytes];
                    Marshal.Copy(ptr, pixels, 0, totalBytes);

                    monochromeBitmap.UnlockBits(bmpData);

                    foreach (byte b in pixels)
                    {
                        hexData.Append(b.ToString("X2"));
                    }

                    // ^GFa,b,c,d,e
                    // a = compression type (A = ASCII Hex)
                    // b = total number of bytes in the graphic field
                    // c = number of bytes per row
                    // d = number of rows
                    // e = graphic data
                    return $"A,{totalBytes},{widthBytes},{monochromeBitmap.Height},{hexData.ToString()}";
                }
            }
        }
    }
}

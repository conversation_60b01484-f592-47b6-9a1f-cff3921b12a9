using System;
using System.Collections.Generic;
using System.Text.Json;
using System.IO;

// 简化的模型类用于诊断
public class SimpleLabelSpec
{
    public double LabelWidth { get; set; }
    public double LabelLength { get; set; }
}

public class SimpleElement
{
    public string Type { get; set; } = "";
    public double X { get; set; }
    public double Y { get; set; }
    public string Content { get; set; } = "";
    public double FontSize { get; set; }
    public double Size { get; set; }
}

class Program
{
    static void Main(string[] args)
    {
        Console.WriteLine("=== 化学标签打印诊断工具 ===");
        
        // 读取 label.json 文件
        var labelJsonPath = Path.Combine("ChemLabelerPrinterClient", "label.json");
        if (!File.Exists(labelJsonPath))
        {
            Console.WriteLine($"错误: 找不到文件 {labelJsonPath}");
            return;
        }

        try
        {
            var jsonContent = File.ReadAllText(labelJsonPath);
            Console.WriteLine("成功读取 label.json 文件");
            
            // 解析 JSON
            using var document = JsonDocument.Parse(jsonContent);
            var root = document.RootElement;
            
            // 获取标签规格
            if (root.TryGetProperty("LabelSpecification", out var labelSpecElement) &&
                labelSpecElement.TryGetProperty("Attributes", out var attributesElement))
            {
                var labelWidth = attributesElement.GetProperty("LabelWidth").GetDouble();
                var labelLength = attributesElement.GetProperty("LabelLength").GetDouble();
                
                Console.WriteLine($"标签尺寸: {labelWidth}mm x {labelLength}mm");
                Console.WriteLine($"像素尺寸: {labelWidth * 3.7795275591:F1} x {labelLength * 3.7795275591:F1} pixels (96 DPI)");
            }
            
            // 获取画布内容
            if (root.TryGetProperty("CanvasContent", out var canvasContentElement))
            {
                var canvasContent = canvasContentElement.GetString();
                Console.WriteLine($"\n画布内容长度: {canvasContent?.Length} 字符");
                
                if (!string.IsNullOrEmpty(canvasContent))
                {
                    // 尝试解析画布元素
                    try
                    {
                        var elements = JsonSerializer.Deserialize<List<SimpleElement>>(canvasContent);
                        Console.WriteLine($"解析到 {elements?.Count ?? 0} 个元素:");
                        
                        if (elements != null)
                        {
                            for (int i = 0; i < elements.Count; i++)
                            {
                                var element = elements[i];
                                Console.WriteLine($"  元素 {i + 1}:");
                                Console.WriteLine($"    类型: {element.Type}");
                                Console.WriteLine($"    位置: ({element.X}, {element.Y}) mm");
                                Console.WriteLine($"    像素位置: ({element.X * 3.7795275591:F1}, {element.Y * 3.7795275591:F1})");
                                Console.WriteLine($"    内容: {(element.Content?.Length > 50 ? element.Content.Substring(0, 50) + "..." : element.Content)}");
                                
                                if (element.Type == "text")
                                {
                                    Console.WriteLine($"    字体大小: {element.FontSize}");
                                }
                                else if (element.Type == "qrcode")
                                {
                                    Console.WriteLine($"    二维码大小: {element.Size}");
                                }
                                Console.WriteLine();
                            }
                        }
                    }
                    catch (JsonException ex)
                    {
                        Console.WriteLine($"解析画布内容时出错: {ex.Message}");
                        Console.WriteLine("原始画布内容:");
                        Console.WriteLine(canvasContent);
                    }
                }
            }
            
            Console.WriteLine("\n=== 诊断建议 ===");
            Console.WriteLine("1. 检查元素是否在标签范围内");
            Console.WriteLine("2. 检查文本内容是否为空");
            Console.WriteLine("3. 检查字体大小是否合适");
            Console.WriteLine("4. 检查二维码内容是否有效");
            Console.WriteLine("5. 检查坐标转换是否正确");
            
        }
        catch (Exception ex)
        {
            Console.WriteLine($"处理文件时出错: {ex.Message}");
        }
        
        Console.WriteLine("\n按任意键退出...");
        Console.ReadKey();
    }
}

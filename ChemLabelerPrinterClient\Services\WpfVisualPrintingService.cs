using ChemLabelerPrinterClient.Models;
using System.Collections.Generic;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows;
using System.IO;
using System;
using QRCoder;
using System.Printing; // Added for PrintServer and PrintQueue

namespace ChemLabelerPrinterClient.Services
{
    public class WpfVisualPrintingService : IPrintingService
    {
        public void Print(LabelSpecification? labelSpec, List<CanvasElement>? elements, int quantity, string? printerName)
        {
            if (labelSpec?.Attributes == null || elements == null)
            {
                MessageBox.Show("Label specification or elements are not valid.", "Printing Error", MessageBoxButton.OK, MessageBoxImage.Error);
                return;
            }

            // 添加详细的调试信息
            var debugInfo = $"Label Size: {labelSpec.Attributes.LabelWidth}mm x {labelSpec.Attributes.LabelLength}mm\n";
            debugInfo += $"Elements Count: {elements.Count}\n";
            debugInfo += $"Quantity: {quantity}\n";
            debugInfo += $"Printer: {printerName ?? "Default"}\n";
            debugInfo += $"InitialDpi: {labelSpec.InitialDpi}\n\n";

            // 使用LabelSpecification中的DPI进行转换 (DPI: 1mm = DPI/25.4 pixels)
            var mmToPx = labelSpec.InitialDpi / 25.4;
            var canvasWidth = labelSpec.Attributes.LabelWidth * mmToPx;
            var canvasHeight = labelSpec.Attributes.LabelLength * mmToPx;

            debugInfo += $"DPI转换: {labelSpec.InitialDpi} DPI -> 1mm = {mmToPx:F3} pixels\n";
            debugInfo += $"Canvas计算: {labelSpec.Attributes.LabelWidth}mm x {labelSpec.Attributes.LabelLength}mm -> {canvasWidth:F1} x {canvasHeight:F1} pixels\n";

            var canvas = new Canvas
            {
                Width = canvasWidth,
                Height = canvasHeight,
                Background = Brushes.White,
                ClipToBounds = true // 确保内容不会超出边界
            };

            debugInfo += $"Canvas Size: {canvas.Width:F1} x {canvas.Height:F1} pixels\n\n";

            int elementIndex = 0;
            foreach (var element in elements)
            {
                elementIndex++;
                debugInfo += $"Element {elementIndex}: Type={element.Type}, X={element.X}, Y={element.Y}\n";
                debugInfo += $"  Element实际类型: {element.GetType().Name}\n";
                debugInfo += $"  Content: '{element.Content}'\n";

                UIElement? uiElement = null;
                switch (element.Type)
                {
                    case "text":
                        debugInfo += $"  进入text分支\n";
                        debugInfo += $"  element is TextElement: {element is TextElement}\n";
                        debugInfo += $"  Content不为空: {!string.IsNullOrWhiteSpace(element.Content)}\n";

                        if (element is TextElement textElement)
                        {
                            debugInfo += $"  TextElement转换成功\n";
                            debugInfo += $"  Text: '{textElement.Content}', FontSize={textElement.FontSize}pt\n";

                            if (!string.IsNullOrWhiteSpace(textElement.Content))
                            {
                                debugInfo += $"  开始创建TextBlock\n";

                                // 字体大小已经是pt单位，直接使用
                                var fontSize = Math.Max(8, Math.Min(72, textElement.FontSize));
                                debugInfo += $"  Text: FontSize={fontSize:F1}pt (原始={textElement.FontSize}pt)\n";

                                try
                                {
                                    uiElement = new TextBlock
                                    {
                                        Text = textElement.Content,
                                        FontSize = fontSize,
                                        FontFamily = new FontFamily(textElement.FontFamily ?? "Arial"),
                                        FontWeight = (FontWeight)(new FontWeightConverter().ConvertFromString(textElement.FontWeight ?? "Normal") ?? FontWeights.Normal),
                                        FontStyle = (FontStyle)(new FontStyleConverter().ConvertFromString(textElement.FontStyle ?? "Normal") ?? FontStyles.Normal),
                                        Foreground = (Brush)(new BrushConverter().ConvertFromString(textElement.FontColor ?? "#000000") ?? Brushes.Black),
                                        Background = Brushes.Transparent,
                                        // 统一对齐方式：左上角对齐
                                        HorizontalAlignment = HorizontalAlignment.Left,
                                        VerticalAlignment = VerticalAlignment.Top
                                    };
                                    debugInfo += $"  TextBlock创建成功\n";
                                }
                                catch (Exception ex)
                                {
                                    debugInfo += $"  TextBlock创建失败: {ex.Message}\n";
                                }
                            }
                            else
                            {
                                debugInfo += $"  文本内容为空\n";
                            }
                        }
                        else
                        {
                            debugInfo += $"  TextElement转换失败\n";
                        }
                        break;
                    case "image":
                        if (element is ImageElement imageElement && !string.IsNullOrEmpty(imageElement.Content))
                        {
                            debugInfo += $"  Image: Size={imageElement.Width}x{imageElement.Height}mm\n";
                            try
                            {
                                var bitmap = new BitmapImage();
                                var base64Data = imageElement.Content.Split(',')[1];
                                using (var stream = new MemoryStream(Convert.FromBase64String(base64Data)))
                                {
                                    bitmap.BeginInit();
                                    bitmap.StreamSource = stream;
                                    bitmap.CacheOption = BitmapCacheOption.OnLoad;
                                    bitmap.EndInit();
                                }
                                // 图片尺寸转换：从毫米转换为像素
                                var imageWidthPx = imageElement.Width * mmToPx;
                                var imageHeightPx = imageElement.Height * mmToPx;
                                debugInfo += $"  Image转换后: {imageWidthPx:F1}x{imageHeightPx:F1}px\n";
                                uiElement = new Image
                                {
                                    Source = bitmap,
                                    Width = imageWidthPx,
                                    Height = imageHeightPx,
                                    // 统一对齐方式：左上角对齐
                                    HorizontalAlignment = HorizontalAlignment.Left,
                                    VerticalAlignment = VerticalAlignment.Top,
                                    Stretch = Stretch.Fill
                                };
                            }
                            catch (Exception ex)
                            {
                                debugInfo += $"  Image Error: {ex.Message}\n";
                            }
                        }
                        break;
                    case "qrcode":
                        if (element is QrCodeElement qrCodeElement && !string.IsNullOrEmpty(qrCodeElement.Content))
                        {
                            debugInfo += $"  QRCode: '{qrCodeElement.Content}', Size={qrCodeElement.Size}mm\n";
                            try
                            {
                                var qrGenerator = new QRCodeGenerator();
                                var qrCodeData = qrGenerator.CreateQrCode(qrCodeElement.Content, QRCodeGenerator.ECCLevel.M);
                                var qrCode = new QRCode(qrCodeData);
                                // 生成QR码图片，不包含边距 (最后参数false = 无边距)
                                var qrCodeImage = qrCode.GetGraphic(20, qrCodeElement.FillColor ?? "#000000", "#FFFFFF", false);
                                debugInfo += $"  QRCode生成: 无边距模式\n";
                                using (var stream = new MemoryStream())
                                {
                                    qrCodeImage.Save(stream, System.Drawing.Imaging.ImageFormat.Png);
                                    stream.Position = 0;
                                    var bmp = new BitmapImage();
                                    bmp.BeginInit();
                                    bmp.StreamSource = stream;
                                    bmp.CacheOption = BitmapCacheOption.OnLoad;
                                    bmp.EndInit();
                                    // 二维码尺寸转换：从毫米转换为像素
                                    var qrSizePx = qrCodeElement.Size * mmToPx;
                                    debugInfo += $"  QRCode转换后: {qrSizePx:F1}x{qrSizePx:F1}px\n";
                                    uiElement = new Image
                                    {
                                        Source = bmp,
                                        Width = qrSizePx,
                                        Height = qrSizePx,
                                        // 统一对齐方式：左上角对齐
                                        HorizontalAlignment = HorizontalAlignment.Left,
                                        VerticalAlignment = VerticalAlignment.Top,
                                        Stretch = Stretch.Fill
                                    };
                                }
                            }
                            catch (Exception ex)
                            {
                                debugInfo += $"  QRCode Error: {ex.Message}\n";
                            }
                        }
                        break;
                }

                if (uiElement != null)
                {
                    var pixelX = element.X * mmToPx;
                    var pixelY = element.Y * mmToPx;

                    // 检查元素是否在画布范围内
                    if (pixelX >= 0 && pixelY >= 0 && pixelX < canvasWidth && pixelY < canvasHeight)
                    {
                        debugInfo += $"  Position: ({pixelX:F1}, {pixelY:F1}) pixels - 在范围内\n";

                        Canvas.SetLeft(uiElement, pixelX);
                        Canvas.SetTop(uiElement, pixelY);

                        // 确保元素可见
                        uiElement.Visibility = Visibility.Visible;

                        canvas.Children.Add(uiElement);
                    }
                    else
                    {
                        debugInfo += $"  Position: ({pixelX:F1}, {pixelY:F1}) pixels - 超出范围！\n";
                    }
                }
                else
                {
                    debugInfo += $"  Failed to create UI element\n";
                }
                debugInfo += "\n";
            }

            debugInfo += $"Canvas children count: {canvas.Children.Count}\n\n";

            // 显示调试信息
            MessageBox.Show(debugInfo, "Print Debug Info", MessageBoxButton.OK, MessageBoxImage.Information);

            // Use the specified printerName if provided, otherwise show PrintDialog
            if (!string.IsNullOrEmpty(printerName))
            {
                try
                {
                    PrintServer printServer = new PrintServer();
                    PrintQueue printQueue = printServer.GetPrintQueue(printerName);

                    PrintDialog printDialog = new PrintDialog();
                    printDialog.PrintQueue = printQueue;

                    // Use label specific dimensions
                    var pageSize = new Size(canvasWidth, canvasHeight);

                    // Update PrintTicket - 使用点为单位 (1 inch = 72 points, 1mm = 2.834645669 points)
                    var labelWidthPoints = labelSpec.Attributes.LabelWidth * 2.834645669;
                    var labelHeightPoints = labelSpec.Attributes.LabelLength * 2.834645669;
                    printDialog.PrintTicket.PageMediaSize = new PageMediaSize(labelWidthPoints, labelHeightPoints);

                    // 强制Canvas布局更新
                    canvas.Measure(pageSize);
                    canvas.Arrange(new Rect(0, 0, pageSize.Width, pageSize.Height));
                    canvas.UpdateLayout();

                    // 强制处理所有待处理的布局和渲染操作
                    System.Windows.Threading.Dispatcher.CurrentDispatcher.Invoke(
                        System.Windows.Threading.DispatcherPriority.Render,
                        new Action(() => { }));

                    // 创建高质量的渲染位图
                    var renderBitmap = new RenderTargetBitmap(
                        (int)Math.Ceiling(canvasWidth),
                        (int)Math.Ceiling(canvasHeight),
                        96, 96, PixelFormats.Pbgra32);

                    renderBitmap.Render(canvas);

                    // Diagnostic step: Save the bitmap to a file to verify its content.
                    var encoder = new PngBitmapEncoder();
                    encoder.Frames.Add(BitmapFrame.Create(renderBitmap));
                    var tempFilePath = Path.Combine(Path.GetTempPath(), "label_preview.png");
                    try
                    {
                        using (var stream = new FileStream(tempFilePath, FileMode.Create))
                        {
                            encoder.Save(stream);
                        }
                        MessageBox.Show($"A preview of the label has been saved to: {tempFilePath}", "Diagnostic Info", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"Failed to save diagnostic image: {ex.Message}", "Diagnostic Error", MessageBoxButton.OK, MessageBoxImage.Error);
                    }

                    // 直接打印Canvas而不是转换为Image，这样可以保持矢量质量
                    for (int i = 0; i < quantity; i++)
                    {
                        // 重新测量和布局以确保打印正确
                        canvas.Measure(pageSize);
                        canvas.Arrange(new Rect(0, 0, pageSize.Width, pageSize.Height));
                        canvas.UpdateLayout();

                        printDialog.PrintVisual(canvas, $"ChemLabeler-{i + 1}");
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error printing to {printerName}: {ex.Message}", "Printing Error", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            else
            {
                var printDialog = new PrintDialog();
                if (printDialog.ShowDialog() == true)
                {
                    // Use label specific dimensions
                    var pageSize = new Size(canvasWidth, canvasHeight);

                    // Update PrintTicket - 使用点为单位
                    var labelWidthPoints = labelSpec.Attributes.LabelWidth * 2.834645669;
                    var labelHeightPoints = labelSpec.Attributes.LabelLength * 2.834645669;
                    printDialog.PrintTicket.PageMediaSize = new PageMediaSize(labelWidthPoints, labelHeightPoints);

                    // 强制Canvas布局更新
                    canvas.Measure(pageSize);
                    canvas.Arrange(new Rect(0, 0, pageSize.Width, pageSize.Height));
                    canvas.UpdateLayout();

                    // 强制处理所有待处理的布局和渲染操作
                    System.Windows.Threading.Dispatcher.CurrentDispatcher.Invoke(
                        System.Windows.Threading.DispatcherPriority.Render,
                        new Action(() => { }));

                    // 创建高质量的渲染位图用于诊断
                    var renderBitmap = new RenderTargetBitmap(
                        (int)Math.Ceiling(canvasWidth),
                        (int)Math.Ceiling(canvasHeight),
                        96, 96, PixelFormats.Pbgra32);

                    renderBitmap.Render(canvas);

                    // Diagnostic step: Save the bitmap to a file to verify its content.
                    var encoder = new PngBitmapEncoder();
                    encoder.Frames.Add(BitmapFrame.Create(renderBitmap));
                    var tempFilePath = Path.Combine(Path.GetTempPath(), "label_preview.png");
                    try
                    {
                        using (var stream = new FileStream(tempFilePath, FileMode.Create))
                        {
                            encoder.Save(stream);
                        }
                        MessageBox.Show($"A preview of the label has been saved to: {tempFilePath}", "Diagnostic Info", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"Failed to save diagnostic image: {ex.Message}", "Diagnostic Error", MessageBoxButton.OK, MessageBoxImage.Error);
                    }

                    // 直接打印Canvas而不是转换为Image，这样可以保持矢量质量
                    for (int i = 0; i < quantity; i++)
                    {
                        // 重新测量和布局以确保打印正确
                        canvas.Measure(pageSize);
                        canvas.Arrange(new Rect(0, 0, pageSize.Width, pageSize.Height));
                        canvas.UpdateLayout();

                        printDialog.PrintVisual(canvas, $"ChemLabeler-{i + 1}");
                    }
                }
            }
        }
    }
}
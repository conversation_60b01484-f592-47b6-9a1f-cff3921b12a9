
using ChemLabelerPrinterClient.Models;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;

namespace ChemLabelerPrinterClient.Services
{
    public class FileService : IFileService
    {
        public async Task<LabelData> ReadLabelDataAsync(string filePath)
        {
            var json = await File.ReadAllTextAsync(filePath);
            return JsonSerializer.Deserialize<LabelData>(json) ?? throw new InvalidDataException("Invalid label data file.");
        }
    }
}

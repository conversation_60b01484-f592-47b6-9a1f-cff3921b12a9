
using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace ChemLabelerPrinterClient.Models
{
    public class LabelData
    {
        [JsonPropertyName("Name")]
        public string? Name { get; set; }

        [JsonPropertyName("LabelSpecification")]
        public LabelSpecification? LabelSpecification { get; set; }

        [JsonPropertyName("CanvasContent")]
        public string? CanvasContent { get; set; }
    }

    public class LabelSpecification
    {
        [JsonPropertyName("PaperLength")]
        public double PaperLength { get; set; }

        [JsonPropertyName("PaperWidth")]
        public double PaperWidth { get; set; }

        [JsonPropertyName("MarginTop")]
        public double MarginTop { get; set; }

        [JsonPropertyName("MarginBottom")]
        public double MarginBottom { get; set; }

        [Json<PERSON>ropertyName("MarginLeft")]
        public double MarginLeft { get; set; }

        [Json<PERSON>ropertyName("MarginRight")]
        public double MarginRight { get; set; }

        [Json<PERSON>ropertyName("InitialDpi")]
        public double InitialDpi { get; set; } = 96; // 默认96 DPI

        [JsonPropertyName("Attributes")]
        public LabelAttributes? Attributes { get; set; }
    }

    public class LabelAttributes
    {
        [JsonPropertyName("LabelLength")]
        public double LabelLength { get; set; }

        [JsonPropertyName("LabelWidth")]
        public double LabelWidth { get; set; }

        [JsonPropertyName("Rows")]
        public int Rows { get; set; }

        [JsonPropertyName("Columns")]
        public int Columns { get; set; }

        [JsonPropertyName("RowSpacing")]
        public double RowSpacing { get; set; }

        [JsonPropertyName("ColumnSpacing")]
        public double ColumnSpacing { get; set; }
    }
}

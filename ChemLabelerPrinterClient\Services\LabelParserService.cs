
using ChemLabelerPrinterClient.Models;
using System.Collections.Generic;
using System.Text.Json;
using System.Text.RegularExpressions;

namespace ChemLabelerPrinterClient.Services
{
    public class LabelParserService : ILabelParserService
    {
        public List<string> ExtractVariables(string canvasContent)
        {
            var variables = new List<string>();
            var regex = new Regex("{{(.*?)}}");
            var matches = regex.Matches(canvasContent);
            foreach (Match match in matches)
            {
                variables.Add(match.Groups[1].Value);
            }
            return variables;
        }

        public List<CanvasElement> ParseCanvasContent(string canvasContent, Dictionary<string, string> variableValues)
        {
            if (string.IsNullOrEmpty(canvasContent))
            {
                return new List<CanvasElement>();
            }

            foreach (var variable in variableValues)
            {
                canvasContent = canvasContent.Replace($"{{{{{variable.Key}}}}}'", variable.Value);
            }

            var options = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true,
                Converters = { new System.Text.Json.Serialization.JsonStringEnumConverter() }
            };
            return System.Text.Json.JsonSerializer.Deserialize<List<CanvasElement>>(canvasContent, options) ?? new List<CanvasElement>();
        }
    }
}


using ChemLabelerPrinterClient.Models;
using System.Collections.Generic;
using System.Text.Json;
using System.Text.RegularExpressions;

namespace ChemLabelerPrinterClient.Services
{
    public class LabelParserService : ILabelParserService
    {
        public List<string> ExtractVariables(string canvasContent)
        {
            var variables = new List<string>();
            var regex = new Regex("{{(.*?)}}");
            var matches = regex.Matches(canvasContent);
            foreach (Match match in matches)
            {
                variables.Add(match.Groups[1].Value);
            }
            return variables;
        }

        public List<CanvasElement> ParseCanvasContent(string canvasContent, Dictionary<string, string> variableValues)
        {
            if (string.IsNullOrEmpty(canvasContent))
            {
                return new List<CanvasElement>();
            }

            // 添加调试信息
            System.Diagnostics.Debug.WriteLine($"Original CanvasContent: {canvasContent}");

            foreach (var variable in variableValues)
            {
                var pattern = $"{{{{{variable.Key}}}}}";
                System.Diagnostics.Debug.WriteLine($"Replacing '{pattern}' with '{variable.Value}'");
                canvasContent = canvasContent.Replace(pattern, variable.Value);
            }

            System.Diagnostics.Debug.WriteLine($"After replacement: {canvasContent}");

            try
            {
                var options = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true,
                    Converters = { new System.Text.Json.Serialization.JsonStringEnumConverter() },
                    // 在.NET 7+中，JsonDerivedType属性应该自动工作
                    // 但我们需要确保包含类型信息
                    IncludeFields = false,
                    WriteIndented = false
                };

                // 首先反序列化为JsonElement数组以保留原始数据
                using var document = JsonDocument.Parse(canvasContent);
                var elementsArray = document.RootElement;

                var elements = new List<CanvasElement>();

                foreach (var elementJson in elementsArray.EnumerateArray())
                {
                    var typeProperty = elementJson.GetProperty("type").GetString();
                    CanvasElement element;

                    switch (typeProperty)
                    {
                        case "text":
                            element = JsonSerializer.Deserialize<TextElement>(elementJson.GetRawText(), options) ?? new TextElement();
                            break;
                        case "image":
                            element = JsonSerializer.Deserialize<ImageElement>(elementJson.GetRawText(), options) ?? new ImageElement();
                            break;
                        case "qrcode":
                            element = JsonSerializer.Deserialize<QrCodeElement>(elementJson.GetRawText(), options) ?? new QrCodeElement();
                            break;
                        default:
                            element = JsonSerializer.Deserialize<CanvasElement>(elementJson.GetRawText(), options) ?? new CanvasElement();
                            break;
                    }

                    elements.Add(element);
                    System.Diagnostics.Debug.WriteLine($"Element: Type={element.Type}, ActualType={element.GetType().Name}");
                }

                System.Diagnostics.Debug.WriteLine($"Parsed {elements.Count} elements with correct types");
                return elements;
            }
            catch (JsonException ex)
            {
                System.Diagnostics.Debug.WriteLine($"JSON Parse Error: {ex.Message}");
                return new List<CanvasElement>();
            }
        }
    }
}



using ChemLabelerPrinterClient.Models;
using ChemLabelerPrinterClient.Services;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using System.Printing; // Added for PrintServer

namespace ChemLabelerPrinterClient.ViewModels
{
    public class MainViewModel : INotifyPropertyChanged
    {
        private readonly IFileService _fileService;
        private readonly ILabelParserService _labelParserService;
        private readonly Dictionary<string, IPrintingService> _printingServices;

        private LabelData? _labelData;
        public ObservableCollection<VariableViewModel> Variables { get; } = new ObservableCollection<VariableViewModel>();

        private string _selectedPrintService = "WPF Visual";
        public string SelectedPrintService
        {
            get => _selectedPrintService;
            set
            {
                _selectedPrintService = value;
                OnPropertyChanged(nameof(SelectedPrintService));
            }
        }

        public List<string> PrintServices { get; }

        private int _printQuantity = 1;
        public int PrintQuantity
        {
            get => _printQuantity;
            set
            {
                _printQuantity = value;
                OnPropertyChanged(nameof(PrintQuantity));
            }
        }

        public ObservableCollection<string> AvailablePrinters { get; } = new ObservableCollection<string>();

        private string? _selectedPrinter;
        public string? SelectedPrinter
        {
            get => _selectedPrinter;
            set
            {
                _selectedPrinter = value;
                OnPropertyChanged(nameof(SelectedPrinter));
            }
        }

        public ICommand PrintCommand { get; }

        public MainViewModel()
        {
            _fileService = new FileService();
            _labelParserService = new LabelParserService();
            _printingServices = new Dictionary<string, IPrintingService>
            {
                { "WPF Visual", new WpfVisualPrintingService() },
                { "ZPL", new ZplPrintingService() }
            };
            PrintServices = _printingServices.Keys.ToList();

            PrintCommand = new RelayCommand(Print);

            LoadLabelData();
            LoadAvailablePrinters();
        }

        private void LoadLabelData()
        {
            _labelData = Task.Run(() => _fileService.ReadLabelDataAsync(@"D:\Codes\Temp\ChemLabelerPrinter\label.json")).Result;
            if (_labelData?.CanvasContent == null) return;
            var variableNames = _labelParserService.ExtractVariables(_labelData.CanvasContent);
            foreach (var name in variableNames.Distinct())
            {
                Variables.Add(new VariableViewModel { Name = name, Value = string.Empty });
            }
        }

        private void LoadAvailablePrinters()
        {
            try
            {
                LocalPrintServer printServer = new LocalPrintServer();
                foreach (PrintQueue pq in printServer.GetPrintQueues(new EnumeratedPrintQueueTypes[] { EnumeratedPrintQueueTypes.Local, EnumeratedPrintQueueTypes.Connections }))
                {
                    AvailablePrinters.Add(pq.Name);
                }
                SelectedPrinter = AvailablePrinters.FirstOrDefault();
            }
            catch (Exception ex)
            {
                // Handle exception, e.g., log it or show a message to the user
                // For now, just ensuring it doesn't crash
                System.Diagnostics.Debug.WriteLine($"Error loading printers: {ex.Message}");
            }
        }

        private void Print()
        {
            if (_labelData?.CanvasContent == null || _labelData.LabelSpecification == null || SelectedPrinter == null) return;
            var variableValues = Variables.ToDictionary(v => v.Name, v => v.Value);
            var elements = _labelParserService.ParseCanvasContent(_labelData.CanvasContent, variableValues);
            _printingServices[SelectedPrintService].Print(_labelData.LabelSpecification, elements, PrintQuantity, SelectedPrinter);
        }

        public event PropertyChangedEventHandler? PropertyChanged;
        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    public class VariableViewModel : INotifyPropertyChanged
    {
        private string _name = string.Empty;
        public string Name
        {
            get => _name;
            set
            {
                _name = value;
                OnPropertyChanged(nameof(Name));
            }
        }

        private string _value = string.Empty;
        public string Value
        {
            get => _value;
            set
            {
                _value = value;
                OnPropertyChanged(nameof(Value));
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;
        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    public class RelayCommand : ICommand
    {
        private readonly Action _execute;
        public event System.EventHandler? CanExecuteChanged
        {
            add { }
            remove { }
        }

        public RelayCommand(Action execute)
        {
            _execute = execute;
        }

        public bool CanExecute(object? parameter) => true;

        public void Execute(object? parameter)
        {
            _execute();
        }
    }
}


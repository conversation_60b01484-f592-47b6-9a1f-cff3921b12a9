
using System.Text.Json.Serialization;

namespace ChemLabelerPrinterClient.Models
{
    [JsonDerivedType(typeof(TextElement), "text")]
    [JsonDerivedType(typeof(ImageElement), "image")]
    [JsonDerivedType(typeof(QrCodeElement), "qrcode")]
    public class CanvasElement
    {
        [JsonPropertyName("type")]
        public string? Type { get; set; }

        [JsonPropertyName("x")]
        public double X { get; set; }

        [JsonPropertyName("y")]
        public double Y { get; set; }

        [JsonPropertyName("content")]
        public string? Content { get; set; }

        [JsonPropertyName("rotate")]
        public double Rotate { get; set; }
    }

    public class TextElement : CanvasElement
    {
        [JsonPropertyName("fontSize")]
        public double FontSize { get; set; }

        [JsonPropertyName("fontFamily")]
        public string? FontFamily { get; set; }

        [Json<PERSON>ropertyName("fontWeight")]
        public string? FontWeight { get; set; }

        [JsonPropertyName("fontStyle")]
        public string? FontStyle { get; set; }

        [JsonPropertyName("textDecoration")]
        public string? TextDecoration { get; set; }

        [JsonPropertyName("fillColor")]
        public string? FillColor { get; set; }
    }

    public class ImageElement : CanvasElement
    {
        [JsonPropertyName("width")]
        public double Width { get; set; }

        [JsonPropertyName("height")]
        public double Height { get; set; }
    }

    public class QrCodeElement : CanvasElement
    {
        [JsonPropertyName("size")]
        public double Size { get; set; }

        [JsonPropertyName("errorCorrectionLevel")]
        public string? ErrorCorrectionLevel { get; set; }

        [JsonPropertyName("fillColor")]
        public string? FillColor { get; set; }
    }
}
